// Express App Configuration

const express = require('express');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const hpp = require('hpp');
const morgan = require('morgan');

const app = express();

// Global Middlewares
app.use(helmet());

// JSON body parser 
app.use(express.json());

// Log requests ( only in dev mode )
if(process.env.NODE_ENV === 'development'){
  app.use(morgan('dev'));
};

// Prevent NoSQL Injection 
app.use(mongoSanitize());

// Prevent XSS ( cross-site scripting )
app.use(xss());

// Prevent HTTP Parameter Pullution 
app.use(hpp());

// Response compression 
app.use(compression());

//  Limit repeated requestes to public APIs
app.use(
  rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: "عدد الطلبات كثيرة جدا، حاول مرة أخرى لاحقا"
  })
);

// Routes 
// teest route
app.get('/api/v1/health', ( req, res ) => {
  return res.status(200).json({
    status : 'ناجح',
    message : 'ال API الامن' 
  })
});

app.use('/api/games', require('./routes/game.routes'));

// Error Handler
app.use(require('./middlewares/errorHandler'));

module.exports = app;
