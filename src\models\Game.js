// models/Game.js
const mongoose = require('mongoose');

const GameSchema = new mongoose.Schema({
  name : {
    type: String,
    require: [ true, 'اسم اللعبة مطلوب!' ],
    trim: true,
    unique : [ true, 'اسم اللعبة ده متسجل قبل كده!' ]
  },
  description : {
    type: String,
    max: 500 
  },
  price: {
    type: Number,
    required: [ true, 'سعر اللعبة مطلوب' ],
    min: 0
  },
  duration: {
    type: Number,
    required: [ true, 'مدة اللعبة مطلوبة' ],
    min: 0
  },
  status: {
    type: String,
    enum: [ 'نشطة', 'قيد الصيانة' ]
  },
},
{
  timestamps: true,
});

module.exports = mongoose.model('Game', GameSchema);