<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>الألعاب - نظام إدارة الملاهي</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body
    class="min-h-screen bg-slate-900 text-slate-100"
    style="font-family: 'Cairo', system-ui, sans-serif"
  >
    <!-- تراك للجوال -->
    <div
      id="overlay"
      class="fixed inset-0 bg-black/40 backdrop-blur-sm opacity-0 pointer-events-none transition md:hidden z-40"
    ></div>

    <!-- الشريط الجانبي يمين -->
    <aside
      id="sidebar"
      class="fixed inset-y-0 right-0 w-72 bg-slate-900/95 border-l border-slate-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out translate-x-full md:translate-x-0"
    >
      <div class="h-full flex flex-col">
        <div
          class="flex items-center justify-between px-4 py-4 border-b border-slate-800"
        >
          <a href="dashboard.html" class="flex items-center gap-2">
            <span
              class="inline-flex items-center justify-center w-9 h-9 rounded-xl bg-indigo-600 text-white"
              >🎡</span
            >
            <span class="text-lg font-bold">الملاهي</span>
          </a>
          <button
            id="closeSidebarBtn"
            class="md:hidden inline-flex items-center justify-center w-9 h-9 rounded-lg hover:bg-slate-800 transition"
            aria-label="إغلاق"
          >
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
              <path
                d="M6 6l12 12M18 6l-12 12"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <nav class="px-2 py-3 flex-1 overflow-y-auto">
          <ul class="space-y-1 text-sm">
            <li>
              <a
                href="dashboard.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >لوحة التحكم</a
              >
            </li>
            <li>
              <a
                href="ticket.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >إصدار التذاكر</a
              >
            </li>
            <li>
              <a
                href="scan.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >فحص التذاكر</a
              >
            </li>
            <li>
              <a
                href="games.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg bg-indigo-600 text-white shadow nav-link"
                >الألعاب</a
              >
            </li>
            <li>
              <a
                href="reports.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >التقارير</a
              >
            </li>
            <li>
              <a
                href="mobile.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >نسخة الموظف</a
              >
            </li>
          </ul>
        </nav>
        <div class="px-3 py-3 border-t border-slate-800 relative">
          <button
            id="profileBtn"
            class="w-full flex items-center justify-between gap-3 px-3 py-2 rounded-lg bg-slate-800/60 hover:bg-slate-800 transition"
          >
            <span class="flex items-center gap-3"
              ><span
                class="inline-flex items-center justify-center w-9 h-9 rounded-full bg-indigo-600 text-white text-sm font-semibold"
                >م</span
              ><span class="text-sm">مرحباً، مدير</span></span
            >
            <svg
              class="w-4 h-4 opacity-80"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06.02L10 11.17l3.71-3.94a.75.75 0 111.08 1.04l-4.24 4.5a.75.75 0 01-1.08 0l-4.24-4.5a.75.75 0 01.02-1.06z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <div
            id="profileMenu"
            class="absolute bottom-16 end-3 w-52 bg-slate-900 border border-slate-800 rounded-xl shadow-lg p-1.5 origin-bottom-right scale-95 opacity-0 pointer-events-none transition z-50"
          >
            <a
              href="#profile"
              class="block px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800 transition"
              >الملف الشخصي</a
            >
            <a
              href="#settings"
              class="block px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800 transition"
              >الإعدادات</a
            >
            <div class="my-1 border-t border-slate-800"></div>
            <a
              href="index.html"
              class="block px-3 py-2 rounded-lg text-rose-400 hover:bg-rose-500/10 transition"
              >تسجيل الخروج</a
            >
          </div>
        </div>
      </div>
    </aside>

    <!-- شريط علوي للجوال -->
    <header
      class="md:hidden sticky top-0 z-30 bg-slate-900/90 backdrop-blur border-b border-slate-800"
    >
      <div class="px-4 py-3 flex items-center justify-between">
        <button
          id="openSidebarBtn"
          class="inline-flex items-center gap-2 px-3 py-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition"
          aria-label="فتح القائمة"
        >
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
            <path
              d="M4 7h16M4 12h16M4 17h16"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
            /></svg
          >القائمة
        </button>
        <div class="flex items-center gap-2">
          <span
            class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-indigo-600 text-white"
            >🎡</span
          ><span class="font-semibold">الملاهي</span>
        </div>
        <span class="w-8"></span>
      </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="min-h-screen md:mr-72 p-4 lg:p-6 space-y-6">
      <section class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4">
        <div class="flex items-center justify-between flex-wrap gap-3 mb-4">
          <h2 class="font-bold">إدارة الألعاب</h2>
          <button
            id="addBtn"
            class="px-4 py-2 rounded-xl bg-indigo-600 text-white hover:bg-indigo-700"
          >
            إضافة لعبة
          </button>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full text-sm">
            <thead>
              <tr class="text-slate-400">
                <th class="text-right py-2">#</th>
                <th class="text-right py-2">اسم اللعبة</th>
                <th class="text-right py-2">القسم</th>
                <th class="text-right py-2">السعر (ر.س)</th>
                <th class="text-right py-2">الإجراءات</th>
              </tr>
            </thead>
            <tbody id="gamesBody"></tbody>
          </table>
        </div>
      </section>

      <!-- نافذة منبثقة -->
      <div
        id="modal"
        class="fixed inset-0 bg-black/30 hidden items-center justify-center p-4"
      >
        <div
          class="bg-slate-900 w-full max-w-md rounded-2xl p-4 border border-slate-700"
        >
          <h3 id="modalTitle" class="font-bold mb-3">إضافة لعبة</h3>
          <form id="gameForm" class="space-y-3">
            <input type="hidden" id="gameId" />
            <div>
              <label class="block text-sm mb-1" for="gameName"
                >اسم اللعبة</label
              >
              <input
                id="gameName"
                type="text"
                required
                class="w-full rounded-xl border border-slate-700 bg-slate-900 px-3 py-2 focus:ring-2 focus:ring-indigo-500 outline-none text-slate-100"
              />
            </div>
            <div>
              <label class="block text-sm mb-1" for="gameCategory">القسم</label>
              <select
                id="gameCategory"
                class="w-full rounded-xl border border-slate-700 bg-slate-900 px-3 py-2 focus:ring-2 focus:ring-indigo-500 outline-none text-slate-100"
              >
                <option>عائلي</option>
                <option>مغامرات</option>
                <option>أطفال</option>
              </select>
            </div>
            <div>
              <label class="block text-sm mb-1" for="gamePrice"
                >السعر (ر.س)</label
              >
              <input
                id="gamePrice"
                type="number"
                min="0"
                value="30"
                class="w-full rounded-xl border border-slate-700 bg-slate-900 px-3 py-2 focus:ring-2 focus:ring-indigo-500 outline-none text-slate-100"
              />
            </div>
            <div class="flex items-center justify-end gap-2 pt-2">
              <button
                type="button"
                id="cancelBtn"
                class="px-4 py-2 rounded-xl border border-slate-700 hover:bg-slate-800"
              >
                إلغاء
              </button>
              <button
                type="submit"
                class="px-4 py-2 rounded-xl bg-indigo-600 text-white hover:bg-indigo-700"
              >
                حفظ
              </button>
            </div>
          </form>
        </div>
      </div>
    </main>

    <main class="max-w-6xl mx-auto px-4 py-6">
      <section class="bg-white border border-gray-200 rounded-2xl p-4">
        <div class="flex items-center justify-between flex-wrap gap-3 mb-4">
          <h2 class="font-bold">إدارة الألعاب</h2>
          <button
            id="addBtn"
            class="px-4 py-2 rounded-xl bg-indigo-600 text-white hover:bg-indigo-700"
          >
            إضافة لعبة
          </button>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full text-sm">
            <thead>
              <tr class="text-gray-500">
                <th class="text-right py-2">#</th>
                <th class="text-right py-2">اسم اللعبة</th>
                <th class="text-right py-2">القسم</th>
                <th class="text-right py-2">السعر (ر.س)</th>
                <th class="text-right py-2">الإجراءات</th>
              </tr>
            </thead>
            <tbody id="gamesBody"></tbody>
          </table>
        </div>
      </section>

      <!-- نافذة منبثقة -->
      <div
        id="modal"
        class="fixed inset-0 bg-black/30 hidden items-center justify-center p-4"
      >
        <div class="bg-white w-full max-w-md rounded-2xl p-4">
          <h3 id="modalTitle" class="font-bold mb-3">إضافة لعبة</h3>
          <form id="gameForm" class="space-y-3">
            <input type="hidden" id="gameId" />
            <div>
              <label class="block text-sm mb-1" for="gameName"
                >اسم اللعبة</label
              >
              <input
                id="gameName"
                type="text"
                required
                class="w-full rounded-xl border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-indigo-200 outline-none"
              />
            </div>
            <div>
              <label class="block text-sm mb-1" for="gameCategory">القسم</label>
              <select
                id="gameCategory"
                class="w-full rounded-xl border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-indigo-200 outline-none"
              >
                <option>عائلي</option>
                <option>مغامرات</option>
                <option>أطفال</option>
              </select>
            </div>
            <div>
              <label class="block text-sm mb-1" for="gamePrice"
                >السعر (ر.س)</label
              >
              <input
                id="gamePrice"
                type="number"
                min="0"
                value="30"
                class="w-full rounded-xl border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-indigo-200 outline-none"
              />
            </div>
            <div class="flex items-center justify-end gap-2 pt-2">
              <button
                type="button"
                id="cancelBtn"
                class="px-4 py-2 rounded-xl border border-gray-300 hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                class="px-4 py-2 rounded-xl bg-indigo-600 text-white hover:bg-indigo-700"
              >
                حفظ
              </button>
            </div>
          </form>
        </div>
      </div>
    </main>

    <script>
      const modal = document.getElementById("modal");
      const modalTitle = document.getElementById("modalTitle");
      const form = document.getElementById("gameForm");
      const body = document.getElementById("gamesBody");

      let games = [
        { id: 1, name: "القطار السريع", category: "مغامرات", price: 50 },
        { id: 2, name: "بيت الرعب", category: "مغامرات", price: 40 },
        { id: 3, name: "عجلة دوارة", category: "عائلي", price: 30 },
      ];

      function render() {
        body.innerHTML = games
          .map(
            (g, i) => `
        <tr class="border-t">
          <td class="py-2">${i + 1}</td>
          <td>${g.name}</td>
          <td>${g.category}</td>
          <td>${g.price}</td>
          <td>
            <button class="text-indigo-600 hover:underline" onclick="editGame(${
              g.id
            })">تعديل</button>
            <span class="mx-1">|</span>
            <button class="text-rose-600 hover:underline" onclick="deleteGame(${
              g.id
            })">حذف</button>
          </td>
        </tr>`
          )
          .join("");
      }

      render();

      document.getElementById("addBtn").addEventListener("click", () => {
        openModal();
      });

      document
        .getElementById("cancelBtn")
        .addEventListener("click", closeModal);

      function openModal(game) {
        modal.classList.remove("hidden");
        modal.classList.add("flex");
        if (game) {
          modalTitle.textContent = "تعديل لعبة";
          document.getElementById("gameId").value = game.id;
          document.getElementById("gameName").value = game.name;
          document.getElementById("gameCategory").value = game.category;
          document.getElementById("gamePrice").value = game.price;
        } else {
          modalTitle.textContent = "إضافة لعبة";
          form.reset();
          document.getElementById("gameId").value = "";
        }
      }

      function closeModal() {
        modal.classList.add("hidden");
        modal.classList.remove("flex");
      }

      form.addEventListener("submit", (e) => {
        e.preventDefault();
        const id = document.getElementById("gameId").value;
        const name = document.getElementById("gameName").value.trim();
        const category = document.getElementById("gameCategory").value;
        const price = +document.getElementById("gamePrice").value;
        if (!name) return alert("يرجى إدخال اسم اللعبة");
        if (id) {
          const idx = games.findIndex((g) => g.id == id);
          games[idx] = { id: +id, name, category, price };
        } else {
          const newId = Math.max(0, ...games.map((g) => g.id)) + 1;
          games.push({ id: newId, name, category, price });
        }
        closeModal();
        render();
      });

      window.editGame = function (id) {
        const g = games.find((x) => x.id === id);
        openModal(g);
      };

      window.deleteGame = function (id) {
        if (!confirm("هل أنت متأكد من الحذف؟")) return;
        games = games.filter((g) => g.id !== id);
        render();
      };
    </script>
  </body>
</html>
