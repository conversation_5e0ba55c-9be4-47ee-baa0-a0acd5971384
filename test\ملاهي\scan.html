<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>فحص التذكرة - نظام إدارة الملاهي</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body
    class="min-h-screen bg-slate-900 text-slate-100"
    style="font-family: 'Cairo', system-ui, sans-serif"
  >
    <!-- تراك للجوال -->
    <div
      id="overlay"
      class="fixed inset-0 bg-black/40 backdrop-blur-sm opacity-0 pointer-events-none transition md:hidden z-40"
    ></div>

    <!-- الشريط الجانبي يمين -->
    <aside
      id="sidebar"
      class="fixed inset-y-0 right-0 w-72 bg-slate-900/95 border-l border-slate-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out translate-x-full md:translate-x-0"
    >
      <div class="h-full flex flex-col">
        <div
          class="flex items-center justify-between px-4 py-4 border-b border-slate-800"
        >
          <a href="dashboard.html" class="flex items-center gap-2">
            <span
              class="inline-flex items-center justify-center w-9 h-9 rounded-xl bg-indigo-600 text-white"
              >🎡</span
            >
            <span class="text-lg font-bold">الملاهي</span>
          </a>
          <button
            id="closeSidebarBtn"
            class="md:hidden inline-flex items-center justify-center w-9 h-9 rounded-lg hover:bg-slate-800 transition"
            aria-label="إغلاق"
          >
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
              <path
                d="M6 6l12 12M18 6l-12 12"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <nav class="px-2 py-3 flex-1 overflow-y-auto">
          <ul class="space-y-1 text-sm">
            <li>
              <a
                href="dashboard.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >لوحة التحكم</a
              >
            </li>
            <li>
              <a
                href="ticket.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >إصدار التذاكر</a
              >
            </li>
            <li>
              <a
                href="scan.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg bg-indigo-600 text-white shadow nav-link"
                >فحص التذاكر</a
              >
            </li>
            <li>
              <a
                href="games.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >الألعاب</a
              >
            </li>
            <li>
              <a
                href="reports.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >التقارير</a
              >
            </li>
            <li>
              <a
                href="mobile.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >نسخة الموظف</a
              >
            </li>
          </ul>
        </nav>
        <div class="px-3 py-3 border-t border-slate-800 relative">
          <button
            id="profileBtn"
            class="w-full flex items-center justify-between gap-3 px-3 py-2 rounded-lg bg-slate-800/60 hover:bg-slate-800 transition"
          >
            <span class="flex items-center gap-3"
              ><span
                class="inline-flex items-center justify-center w-9 h-9 rounded-full bg-indigo-600 text-white text-sm font-semibold"
                >م</span
              ><span class="text-sm">مرحباً، مدير</span></span
            >
            <svg
              class="w-4 h-4 opacity-80"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06.02L10 11.17l3.71-3.94a.75.75 0 111.08 1.04l-4.24 4.5a.75.75 0 01-1.08 0l-4.24-4.5a.75.75 0 01.02-1.06z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <div
            id="profileMenu"
            class="absolute bottom-16 end-3 w-52 bg-slate-900 border border-slate-800 rounded-xl shadow-lg p-1.5 origin-bottom-right scale-95 opacity-0 pointer-events-none transition z-50"
          >
            <a
              href="#profile"
              class="block px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800 transition"
              >الملف الشخصي</a
            >
            <a
              href="#settings"
              class="block px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800 transition"
              >الإعدادات</a
            >
            <div class="my-1 border-t border-slate-800"></div>
            <a
              href="index.html"
              class="block px-3 py-2 rounded-lg text-rose-400 hover:bg-rose-500/10 transition"
              >تسجيل الخروج</a
            >
          </div>
        </div>
      </div>
    </aside>

    <!-- شريط علوي للجوال -->
    <header
      class="md:hidden sticky top-0 z-30 bg-slate-900/90 backdrop-blur border-b border-slate-800"
    >
      <div class="px-4 py-3 flex items-center justify-between">
        <button
          id="openSidebarBtn"
          class="inline-flex items-center gap-2 px-3 py-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition"
          aria-label="فتح القائمة"
        >
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
            <path
              d="M4 7h16M4 12h16M4 17h16"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
            /></svg
          >القائمة
        </button>
        <div class="flex items-center gap-2">
          <span
            class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-indigo-600 text-white"
            >🎡</span
          ><span class="font-semibold">الملاهي</span>
        </div>
        <span class="w-8"></span>
      </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="min-h-screen md:mr-72 p-4 lg:p-6 space-y-6">
      <section class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div
          class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4 lg:col-span-2"
        >
          <h2 class="font-bold mb-4">فحص تذكرة باستخدام QR</h2>
          <div
            class="aspect-video bg-slate-800 border border-dashed border-slate-700 rounded-xl flex items-center justify-center text-slate-400"
          >
            كاميرا (عنصر نائب)
          </div>
          <div class="mt-4 flex items-center gap-3">
            <input
              id="manualCode"
              type="text"
              placeholder="أدخل رقم التذكرة يدوياً"
              class="flex-1 rounded-xl border border-slate-700 bg-slate-900 px-3 py-2 focus:ring-2 focus:ring-indigo-500 outline-none placeholder-slate-500 text-slate-100"
            />
            <button
              id="validateBtn"
              class="px-4 py-2 rounded-xl bg-indigo-600 text-white hover:bg-indigo-700"
            >
              تحقق
            </button>
          </div>
        </div>

        <div class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4">
          <h2 class="font-bold mb-3">النتيجة</h2>
          <div
            id="result"
            class="rounded-xl p-4 bg-slate-900 text-sm text-slate-300 border border-slate-700"
          >
            لا توجد نتيجة بعد
          </div>
        </div>
      </section>
    </main>

    <script>
      // قائمة صورية من التذاكر الصالحة
      const validTickets = new Set(["T-2001", "T-2002", "T-2003", "T-1001"]);
      const input = document.getElementById("manualCode");
      const result = document.getElementById("result");
      document.getElementById("validateBtn").addEventListener("click", () => {
        const code = input.value.trim();
        if (!code) return alert("يرجى إدخال رقم التذكرة");
        if (validTickets.has(code)) {
          result.className =
            "rounded-xl p-4 text-sm bg-emerald-900/20 text-emerald-300 border border-emerald-700";
          result.textContent = `التذكرة ${code} صالحة، أهلاً بك!`;
        } else {
          result.className =
            "rounded-xl p-4 text-sm bg-rose-900/20 text-rose-300 border border-rose-700";
          result.textContent = `التذكرة ${code} غير صالحة`;
        }
      });

      // عنصر نائب: عند دمج ماسح QR حقيقي، استدعِ نفس التحقق بالقيمة المقروءة
      function onQrScanned(fakeValue) {
        input.value = fakeValue;
        document.getElementById("validateBtn").click();
      }
      window.onQrScanned = onQrScanned;

      // فتح/إغلاق الشريط للجوال
      const sidebar = document.getElementById("sidebar");
      const overlay = document.getElementById("overlay");
      const openBtn = document.getElementById("openSidebarBtn");
      const closeBtn = document.getElementById("closeSidebarBtn");
      const openSidebar = () => {
        sidebar.classList.remove("translate-x-full");
        overlay.classList.remove("pointer-events-none");
        overlay.classList.add("opacity-100");
        overlay.classList.remove("opacity-0");
      };
      const closeSidebar = () => {
        sidebar.classList.add("translate-x-full");
        overlay.classList.add("pointer-events-none");
        overlay.classList.remove("opacity-100");
        overlay.classList.add("opacity-0");
      };
      openBtn?.addEventListener("click", openSidebar);
      closeBtn?.addEventListener("click", closeSidebar);
      overlay?.addEventListener("click", closeSidebar);
      const mq = window.matchMedia("(min-width:768px)");
      const handleMQ = () => {
        if (mq.matches) {
          sidebar.classList.remove("translate-x-full");
          overlay.classList.add("pointer-events-none", "opacity-0");
          overlay.classList.remove("opacity-100");
        } else {
          sidebar.classList.add("translate-x-full");
        }
      };
      handleMQ();
      mq.addEventListener?.("change", handleMQ);

      // القائمة المنسدلة للمستخدم
      const profileBtn = document.getElementById("profileBtn");
      const profileMenu = document.getElementById("profileMenu");
      let dropOpen = false;
      const openDrop = () => {
        dropOpen = true;
        profileMenu.classList.remove(
          "opacity-0",
          "scale-95",
          "pointer-events-none"
        );
        profileMenu.classList.add(
          "opacity-100",
          "scale-100",
          "pointer-events-auto"
        );
      };
      const closeDrop = () => {
        dropOpen = false;
        profileMenu.classList.add(
          "opacity-0",
          "scale-95",
          "pointer-events-none"
        );
        profileMenu.classList.remove(
          "opacity-100",
          "scale-100",
          "pointer-events-auto"
        );
      };
      closeDrop();
      profileBtn?.addEventListener("click", (e) => {
        e.stopPropagation();
        dropOpen ? closeDrop() : openDrop();
      });
      document.addEventListener("click", (e) => {
        if (
          dropOpen &&
          !profileMenu.contains(e.target) &&
          !profileBtn.contains(e.target)
        )
          closeDrop();
      });
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape") {
          closeDrop();
          if (!mq.matches) closeSidebar();
        }
      });
    </script>
  </body>
</html>
