// routes/game.routes.js

const express = require('express');
const router = express.Router();

// Imports Controllers
const {
  getGames, createGame, updateGame, deleteGame
} = require('../controllers/game.controller');

// Import Validations
const {
  createGameValidation, idParamValidation, updateGameValidation
} = require('../validations/gameValidation')

/**
 * @desc Create new game
 * @method POST /api/games
*/
router.route('/')
  .get( getGames )
  .post( createGameValidation, createGame )

router.route('/:id')
  .patch( idParamValidation, updateGameValidation, updateGame )
  .delete( idParamValidation, deleteGame )

module.exports = router;