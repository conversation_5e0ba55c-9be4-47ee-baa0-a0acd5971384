{"name": "celebrate", "version": "15.0.3", "description": "A joi validation middleware for Express.", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "is-ci 'test:ci' 'test:local'", "test:local": "jest --watch --verbose", "test:ci": "jest --ci", "benchmark": "node utils/benchmark", "toc": "node utils/generate-toc", "version": "npm run toc && git add README.md"}, "repository": {"type": "git", "url": "git+https://github.com/arb/celebrate.git"}, "keywords": ["joi", "validation", "express", "middleware"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/arb/celebrate/issues"}, "homepage": "https://github.com/arb/celebrate#readme", "dependencies": {"escape-html": "1.0.3", "joi": "17.x.x", "lodash": "4.17.x"}, "devDependencies": {"@hapi/teamwork": "5.1.0", "@types/express": "4.x.x", "artificial": "1.x.x", "benchmark": "2.1.x", "body-parser": "1.19.x", "cookie-parser": "1.4.x", "cookie-signature": "1.1.x", "eslint": "8.x.x", "eslint-config-airbnb-base": "15.x.x", "eslint-plugin-import": "2.x.x", "expect": "27.x.x", "express": "next", "faker": "5.5.x", "is-ci-cli": "2.x.x", "jest": "27.x.x", "jest-runner-eslint": "1.x.x", "markdown-toc": "1.2.x"}}