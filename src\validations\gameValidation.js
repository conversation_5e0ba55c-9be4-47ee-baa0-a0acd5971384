const { Joi, Segments, celebrate } = require('celebrate');

// Validation middleware for creating game
const createGameValidation = celebrate({
  [Segments.BODY]: Joi.object().keys({
    name: Joi.string()
      .trim()
      .required()
      .messages({
        'string.empty': 'اسم اللعبة مطلوب!',
        'any.required': 'اسم اللعبة مطلوب!',
      }),

    description: Joi.string()
      .max(500)
      .messages({
        'string.max': 'الوصف لا يمكن أن يزيد عن 500 حرف',
      }),

    price: Joi.number()
      .min(0)
      .required()
      .messages({
        'number.base': 'السعر لازم يكون رقم',
        'number.min': 'السعر لازم يكون أكبر من أو يساوي 0',
        'any.required': 'سعر اللعبة مطلوب!',
      }),

    duration: Joi.number()
      .min(0)
      .required()
      .messages({
        'number.base': 'المدة لازم تكون رقم',
        'number.min': 'المدة لازم تكون أكبر من أو تساوي 0',
        'any.required': 'مدة اللعبة مطلوبة!',
      }),

    status: Joi.string()
    .valid('نشطة', 'قيد الصيانة')
    .required()
      .messages({
        'any.only': 'الحالة لازم تكون نشطة أو قيد الصيانة فقط',
        'any.required': '❌ الحالة مطلوبة!',
      }),
  }),
});

// Validation rules for updating game
const updateGameValidation = celebrate({
  [Segments.BODY]: Joi.object().keys({
    name: Joi.string()
      .trim()
      .messages({
        'string.empty': '❌ اسم اللعبة لا يمكن أن يكون فارغ!',
      }),

    description: Joi.string()
      .max(500)
      .messages({
        'string.max': '❌ الوصف لا يمكن أن يزيد عن 500 حرف',
      }),

    price: Joi.number()
      .min(0)
      .messages({
        'number.base': '❌ السعر لازم يكون رقم',
        'number.min': '❌ السعر لازم يكون أكبر من أو يساوي 0',
      }),

    duration: Joi.number()
      .min(0)
      .messages({
        'number.base': '❌ المدة لازم تكون رقم',
        'number.min': '❌ المدة لازم تكون أكبر من أو تساوي 0',
      }),

    status: Joi.string()
      .valid('نشطة', 'قيد الصيانة')
      .messages({
        'any.only': '❌ الحالة لازم تكون نشطة أو قيد الصيانة فقط',
      }),
  }),
});
// Id Param Validation
const idParamValidation = celebrate({
  [Segments.PARAMS]: Joi.object({
    id: Joi.string().hex().length(24).required().messages({
      'any.required': '❌ المعرّف مطلوب',
      'string.hex': '❌ المعرّف غير صالح',
      'string.length': '❌ المعرّف غير صالح',
    }),
  }),
});

module.exports = {
  createGameValidation,
  updateGameValidation,
  idParamValidation
};
