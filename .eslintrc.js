// ESLint Configuration

module.exports = {
  env: {
    node: true,     // running on Node.js
    es2021: true,   // latest ECMAScript features
  },
  extends: ['eslint:recommended', 'prettier'], // recommended rules + prettier
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module',
  },
  rules: {
    'no-unused-vars': 'warn', // warn if variable declared but not used
    eqeqeq: 'error',          // force strict equality ===
  },
};
