const Game = require('../models/Game');

/**
 * @desc Create a new game
 * @param {Object} data - Game data
 * @returns {Promise<Object>} - Created game document
 */
async function createGame(data) {
  return await Game.create(data);
}

/**
 * @desc Get all games
 * @returns {Promise<Array>} - List of games
*/
async function getGames() {
  return await Game.find().lean();
}

/**
 * @desc Update game by ID
 * @param {String} id - Game ID
 * @param {Object} data - Updated fields
 * @returns {Promise<Object|null>} - Updated game or null if not found
*/
async function updateGame(id, data) {
  return await Game.findByIdAndUpdate(
    id,
    { $set: data },
    { new: true, runValidators: true },
  );
}


/**
 * @desc Delete game by ID
 * @param {String} id - Game ID
 * @returns {Promise<Object|null>} - Deleted game or null if not found
*/
// Delete game
async function deleteGame(id) {
  return await Game.findByIdAndDelete(id);
}


module.exports = {
  getGames,
  createGame,
  updateGame,
  deleteGame
};