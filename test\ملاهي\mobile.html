<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>نسخة الموظف - نظام إدارة الملاهي</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body
    class="min-h-screen bg-slate-900 text-slate-100"
    style="font-family: 'Cairo', system-ui, sans-serif"
  >
    <!-- تراك للجوال -->
    <div
      id="overlay"
      class="fixed inset-0 bg-black/40 backdrop-blur-sm opacity-0 pointer-events-none transition md:hidden z-40"
    ></div>

    <!-- الشريط الجانبي يمين -->
    <aside
      id="sidebar"
      class="fixed inset-y-0 right-0 w-72 bg-slate-900/95 border-l border-slate-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out translate-x-full md:translate-x-0"
    >
      <div class="h-full flex flex-col">
        <div
          class="flex items-center justify-between px-4 py-4 border-b border-slate-800"
        >
          <a href="dashboard.html" class="flex items-center gap-2">
            <span
              class="inline-flex items-center justify-center w-9 h-9 rounded-xl bg-indigo-600 text-white"
              >🎡</span
            >
            <span class="text-lg font-bold">الملاهي</span>
          </a>
          <button
            id="closeSidebarBtn"
            class="md:hidden inline-flex items-center justify-center w-9 h-9 rounded-lg hover:bg-slate-800 transition"
            aria-label="إغلاق"
          >
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
              <path
                d="M6 6l12 12M18 6l-12 12"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <nav class="px-2 py-3 flex-1 overflow-y-auto">
          <ul class="space-y-1 text-sm">
            <li>
              <a
                href="dashboard.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >لوحة التحكم</a
              >
            </li>
            <li>
              <a
                href="ticket.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >إصدار التذاكر</a
              >
            </li>
            <li>
              <a
                href="scan.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >فحص التذاكر</a
              >
            </li>
            <li>
              <a
                href="games.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >الألعاب</a
              >
            </li>
            <li>
              <a
                href="reports.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >التقارير</a
              >
            </li>
            <li>
              <a
                href="mobile.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg bg-indigo-600 text-white shadow nav-link"
                >نسخة الموظف</a
              >
            </li>
          </ul>
        </nav>
        <div class="px-3 py-3 border-t border-slate-800 relative">
          <button
            id="profileBtn"
            class="w-full flex items-center justify-between gap-3 px-3 py-2 rounded-lg bg-slate-800/60 hover:bg-slate-800 transition"
          >
            <span class="flex items-center gap-3"
              ><span
                class="inline-flex items-center justify-center w-9 h-9 rounded-full bg-indigo-600 text-white text-sm font-semibold"
                >م</span
              ><span class="text-sm">مرحباً، مدير</span></span
            >
            <svg
              class="w-4 h-4 opacity-80"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06.02L10 11.17l3.71-3.94a.75.75 0 111.08 1.04l-4.24 4.5a.75.75 0 01-1.08 0l-4.24-4.5a.75.75 0 01.02-1.06z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <div
            id="profileMenu"
            class="absolute bottom-16 end-3 w-52 bg-slate-900 border border-slate-800 rounded-xl shadow-lg p-1.5 origin-bottom-right scale-95 opacity-0 pointer-events-none transition z-50"
          >
            <a
              href="#profile"
              class="block px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800 transition"
              >الملف الشخصي</a
            >
            <a
              href="#settings"
              class="block px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800 transition"
              >الإعدادات</a
            >
            <div class="my-1 border-t border-slate-800"></div>
            <a
              href="index.html"
              class="block px-3 py-2 rounded-lg text-rose-400 hover:bg-rose-500/10 transition"
              >تسجيل الخروج</a
            >
          </div>
        </div>
      </div>
    </aside>

    <!-- شريط علوي للجوال -->
    <header
      class="md:hidden sticky top-0 z-30 bg-slate-900/90 backdrop-blur border-b border-slate-800"
    >
      <div class="px-4 py-3 flex items-center justify-between">
        <button
          id="openSidebarBtn"
          class="inline-flex items-center gap-2 px-3 py-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition"
          aria-label="فتح القائمة"
        >
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
            <path
              d="M4 7h16M4 12h16M4 17h16"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
            /></svg
          >القائمة
        </button>
        <div class="flex items-center gap-2">
          <span
            class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-indigo-600 text-white"
            >🎡</span
          ><span class="font-semibold">الملاهي</span>
        </div>
        <span class="w-8"></span>
      </div>
    </header>

    <main class="max-w-xl mx-auto px-4 py-6">
      <nav class="grid grid-cols-2 gap-3">
        <a
          href="#issue"
          class="rounded-2xl bg-white border border-gray-200 p-4 flex items-center justify-center hover:bg-gray-50"
          >إصدار تذكرة</a
        >
        <a
          href="#scan"
          class="rounded-2xl bg-white border border-gray-200 p-4 flex items-center justify-center hover:bg-gray-50"
          >فحص تذكرة</a
        >
      </nav>

      <section
        id="issue"
        class="mt-6 bg-white border border-gray-200 rounded-2xl p-4"
      >
        <h2 class="font-bold mb-3">إصدار تذكرة سريعة</h2>
        <form id="mobileTicketForm" class="space-y-3" novalidate>
          <div>
            <label class="block text-sm mb-1" for="mCustomer">اسم العميل</label>
            <input
              id="mCustomer"
              type="text"
              required
              class="w-full rounded-xl border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-indigo-200 outline-none"
            />
          </div>
          <div>
            <label class="block text-sm mb-1" for="mGame">اللعبة</label>
            <select
              id="mGame"
              class="w-full rounded-xl border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-indigo-200 outline-none"
            >
              <option>القطار السريع</option>
              <option>بيت الرعب</option>
              <option>عجلة دوارة</option>
            </select>
          </div>
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-sm mb-1" for="mPrice">السعر</label>
              <input
                id="mPrice"
                type="number"
                min="0"
                value="40"
                class="w-full rounded-xl border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-indigo-200 outline-none"
              />
            </div>
            <div>
              <label class="block text-sm mb-1" for="mQty">الكمية</label>
              <input
                id="mQty"
                type="number"
                min="1"
                value="1"
                class="w-full rounded-xl border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-indigo-200 outline-none"
              />
            </div>
          </div>
          <button
            type="submit"
            class="w-full rounded-xl bg-indigo-600 text-white py-2 hover:bg-indigo-700"
          >
            إصدار
          </button>
        </form>
        <div
          id="mToast"
          class="hidden mt-3 rounded-lg bg-emerald-50 border border-emerald-200 text-emerald-700 p-3 text-sm"
        ></div>
      </section>

      <section
        id="scan"
        class="mt-6 bg-white border border-gray-200 rounded-2xl p-4"
      >
        <h2 class="font-bold mb-3">فحص سريع</h2>
        <div
          class="aspect-video bg-gray-100 border border-dashed border-gray-300 rounded-xl flex items-center justify-center text-gray-500"
        >
          كاميرا (عنصر نائب)
        </div>
        <div class="mt-3 flex gap-2">
          <input
            id="mManualCode"
            type="text"
            placeholder="أدخل رقم التذكرة"
            class="flex-1 rounded-xl border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-indigo-200 outline-none"
          />
          <button
            id="mValidate"
            class="px-4 py-2 rounded-xl bg-indigo-600 text-white hover:bg-indigo-700"
          >
            تحقق
          </button>
        </div>
        <div
          id="mResult"
          class="mt-3 rounded-xl p-3 bg-gray-50 text-sm text-gray-600"
        >
          لا توجد نتيجة بعد
        </div>
      </section>
    </main>

    <footer class="mt-8 py-6 text-center text-xs text-gray-500">
      © <span id="year"></span> نظام إدارة الملاهي
    </footer>

    <script>
      document.getElementById("year").textContent = new Date().getFullYear();

      // إصدار تذكرة على المحمول (صوري)
      document
        .getElementById("mobileTicketForm")
        .addEventListener("submit", (e) => {
          e.preventDefault();
          const name = document.getElementById("mCustomer").value.trim();
          const game = document.getElementById("mGame").value;
          const price = +document.getElementById("mPrice").value;
          const qty = +document.getElementById("mQty").value;
          if (!name || qty <= 0 || price < 0)
            return alert("يرجى إدخال بيانات صحيحة");
          const id = "M-" + Math.floor(1000 + Math.random() * 9000);
          const toast = document.getElementById("mToast");
          toast.textContent = `تم إصدار التذكرة ${id} للعميل ${name} (${qty} × ${price} ر.س)`;
          toast.classList.remove("hidden");
          setTimeout(() => toast.classList.add("hidden"), 2500);
        });

      // فحص سريع على المحمول (صوري)
      const valid = new Set(["T-2001", "T-2002", "M-1234"]);
      document.getElementById("mValidate").addEventListener("click", () => {
        const code = document.getElementById("mManualCode").value.trim();
        const box = document.getElementById("mResult");
        if (valid.has(code)) {
          box.className =
            "mt-3 rounded-xl p-3 bg-emerald-50 text-emerald-700 border border-emerald-200";
          box.textContent = `التذكرة ${code} صالحة`;
        } else {
          box.className =
            "mt-3 rounded-xl p-3 bg-rose-50 text-rose-700 border border-rose-200";
          box.textContent = `التذكرة ${code} غير صالحة`;
        }
      });
    </script>
  </body>
</html>
