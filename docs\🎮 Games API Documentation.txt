Base URL
http://localhost:5000/

🔹 Get All Games

Endpoint
GET /api/games

Response (200)
{
  "success": true,
  "count": 2,
  "games": [
    {
      "_id": "64e56c09ef45c2a2f9c12a3b",
      "name": "Roller Coaster",
      "description": "Fast and exciting ride",
      "price": 50,
      "duration": 5,
      "status": "نشطة"
    },
    {
      "_id": "64e56c21ef45c2a2f9c12a3c",
      "name": "Ferris Wheel",
      "description": "Family-friendly fun",
      "price": 20,
      "duration": 10,
      "status": "قيد الصيانة"
    }
  ]
}

🔹 Create Game

Endpoint
POST /api/games

Body
{
  "name": "Haunted House",
  "description": "Scary fun attraction",
  "price": 30,
  "duration": 15,
  "status": "نشطة"
}

Response (201)

{
  "success": true,
  "msg": "✅ تم إنشاء لعبة Haunted House بنجاح"
}

Errors
{
    "success" : false
    "errMsg": "❌ اسم اللعبة مطلوب!",
}

🔹 Update Game

Endpoint
PATCH /api/games/:id

Example
PATCH /api/games/64e56c09ef45c2a2f9c12a3b

Body
{
  "name": "Roller Coaster Extreme",
  "description": "Even faster ride!",
  "price": 60,
  "duration": 6,
  "status": "نشطة"
}

Response (200)
{
  "success": true,
  "msg": "✅ تم تحديث لعبة Roller Coaster Extreme بنجاح"
}

Error (404)
{
  "success": false,
  "errMsg": "❌ اللعبة غير موجودة"
}

🔹 Delete Game

Endpoint
DELETE /api/games/:id

Example
DELETE /api/games/64e56c21ef45c2a2f9c12a3c

Response (200)
{
  "success": true,
  "msg": "🗑️ تم حذف لعبة Ferris Wheel بنجاح"
}

Error (404)
{
  "success": false,
  "errMsg": "❌ اللعبة غير موجودة"
}

📌 Notes
الـ id لازم يكون MongoDB ObjectId (24-char hex).
الحالات المتاحة: "نشطة" أو "قيد الصيانة".