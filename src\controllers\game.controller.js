// controllers/game.controller.js

// Get Services 
const { getGames, createGame, updateGame, deleteGame } = require('../services/game.service');

/**
 * @desc Get All Games
 * @route GET /api/games
 * @access Public
*/
module.exports.getGames = async ( req, res, next ) => {
  try{
    // Get All Games
    const games = await getGames();

    // Return All Games In Response
    return res.status(200).json({
      success: true,
      count: games.length,
      games
    });

  }catch(err){
    next(err)
  };
};



/**
 * @desc Create a new game
 * @route POST /api/game
 * @access Public
*/
module.exports.createGame = async ( req, res, next ) => {
  try{
    // Pick only allowed fields
    const { name, description, price, duration, status } = req.body || { };
    
    //  Call create services 
    const game = await createGame({
      name, description, price, duration, status
    });

    // Return Response
    return res.status(201).json({
      success: true,
      msg: `✅ تم إنشاء لعبة ${game.name} بنجاح`,

    })
    
  }catch(err){
    console.log(err)
    next(err)
  };
};

/**
 * @desc Update existing game with its _id 
 * @route PATCH /api/games/:id
 * @access Public
*/
module.exports.updateGame = async ( req, res, next ) => {
  try{ 
    // Take game id from params
    const { id } = req.params || {}

    // Pick only allowed fields from body 
    const { name, description, price, duration, status } = req.body || {};

    // Call update services 
    const game = await updateGame(
      id, { name, description, price, duration, status }
    );

    // If not found
    if(!game){
      return res.status(404).json({
        success: false,
        errMsg: '❌ اللعبة غير موجودة',
      });
    };

    // Return Response
    return res.status(200).json({
      success: true,
      msg: `✅ تم تحديث لعبة ${game.name} بنجاح`,
    })

  }catch(err){
    next(err)
  };
};

/**
 * @desc DeLete Game
 * @route DELETE /api/games/:id
 * @access Public
*/
module.exports.deleteGame = async ( req, res, next ) => {
  try{
    // Take game id from params
    const { id } = req.params;
  
    // Call delete services 
    const game = await deleteGame(id)

    // If not found
    if(!game){
      return res.status(404).json({
        success: false,
        errMsg: '❌ اللعبة غير موجودة',
      });
    };

  // Return Response
  return res.status(200).json({
    success: true,
    msg: `🗑️ تم حذف لعبة ${game.name} بنجاح`,
  })

  }catch(err){
    console.lot(err)
    next(err)
  }
};