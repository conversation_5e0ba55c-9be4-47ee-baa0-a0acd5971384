// middlewares/errorHandler.js
const { isCelebrateError } = require('celebrate');

const errorHandler = ( err, req, res, next ) => {
  // Celebrate validation error
  if(isCelebrateError(err)){
    const bodyError = err.details.get('body');
    const errMsg = bodyError ? bodyError.details[0].message : '❌ خطأ في البيانات المدخلة'
    
    // Return Error Message
    return res.status(400).json({
      success: false,
      errMsg
    });
  };

  // General error
  return res.status(500).json({
    success: false,
    errMsg: 'حدث خطأ غير متوقع!',
  });
};

module.exports = errorHandler;