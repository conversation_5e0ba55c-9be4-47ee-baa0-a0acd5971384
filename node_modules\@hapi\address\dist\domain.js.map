{"version": 3, "file": "domain.js", "sourceRoot": "", "sources": ["../src/domain.ts"], "names": [], "mappings": ";;;AAAA,2BAA2B;AAE3B,qCAAqC;AAErC,MAAM,mBAAmB,GAAG,CAAC,CAAC;AAC9B,MAAM,YAAY,GAAG,cAAc,CAAC;AACpC,MAAM,iBAAiB,GAAG,wCAAwC,CAAC,CAAC,+BAA+B;AACnG,MAAM,cAAc,GAAG,0CAA0C,CAAC;AAClE,MAAM,iBAAiB,GAAG,6CAA6C,CAAC;AACxE,MAAM,4BAA4B,GAAG,8CAA8C,CAAC;AACpF,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,wBAAwB;AAUzD,SAAS,WAAW,CAAC,IAAS;IAC1B,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AACxB,CAAC;AA0DD;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,MAAc,EAAE,UAAyB,EAAE;IACrE,IAAI,CAAC,MAAM,EAAE;QACT,yBAAyB;QACzB,OAAO,IAAA,kBAAS,EAAC,yBAAyB,CAAC,CAAC;KAC/C;IAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC7D;IAED,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;QACrB,OAAO,IAAA,kBAAS,EAAC,iBAAiB,CAAC,CAAC;KACvC;IAED,MAAM,KAAK,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC,IAAI,CAAC,KAAK,EAAE;QACR,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,EAAE;YAChC,mBAAmB;YACnB,OAAO,IAAA,kBAAS,EAAC,8BAA8B,CAAC,CAAC;SACpD;QAED,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACpC;IAED,IAAI,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QAChC,OAAO,IAAA,kBAAS,EAAC,sBAAsB,CAAC,CAAC;KAC5C;IAED,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;IAE1B,oDAAoD;IAEpD,IAAI,OAAO,CAAC,mBAAmB,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QAClE,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAChC;IAED,MAAM,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,mBAAmB,CAAC;IAE3E,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACnC,IAAI,QAAQ,CAAC,MAAM,GAAG,iBAAiB,EAAE;QACrC,OAAO,IAAA,kBAAS,EAAC,uBAAuB,CAAC,CAAC;KAC7C;IAED,IAAI,OAAO,CAAC,iBAAiB,EAAE;QAC3B,IAAI,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE;YAC7C,OAAO,IAAA,kBAAS,EAAC,2BAA2B,CAAC,CAAC;SACjD;KACJ;IAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,IAAI,IAAI,EAAE;QACN,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACxD,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACtB,OAAO,IAAA,kBAAS,EAAC,uBAAuB,CAAC,CAAC;aAC7C;SACJ;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC3B,OAAO,IAAA,kBAAS,EAAC,uBAAuB,CAAC,CAAC;SAC7C;KACJ;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACtC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,OAAO,IAAA,kBAAS,EAAC,sBAAsB,CAAC,CAAC;SAC5C;QAED,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE;YACrB,OAAO,IAAA,kBAAS,EAAC,qBAAqB,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,IAAI,OAAO,CAAC,eAAe,EAAE;gBACzB,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;oBAC7C,OAAO,IAAA,kBAAS,EAAC,sBAAsB,CAAC,CAAC;iBAC5C;aACJ;iBAAM;gBACH,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;oBAClC,OAAO,IAAA,kBAAS,EAAC,sBAAsB,CAAC,CAAC;iBAC5C;aACJ;SACJ;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC/B,OAAO,IAAA,kBAAS,EAAC,2BAA2B,CAAC,CAAC;aACjD;SACJ;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AA1FD,sCA0FC;AAED;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,MAAc,EAAE,OAAuB;IACjE,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3C,CAAC;AAFD,sCAEC;AAED,SAAS,QAAQ,CAAC,MAAc;IAC5B,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACxC;IAED,IAAI;QACA,OAAO,IAAI,QAAQ,CAAC,UAAU,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC;KAChD;IAAC,OAAO,GAAG,EAAE;QACV,OAAO,MAAM,CAAC;KACjB;AACL,CAAC;AAED,SAAgB,qBAAqB,CAAC,OAAsB;IACxD,IAAI,CAAC,OAAO,EAAE;QACV,OAAO;KACV;IAED,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;KAC3E;IAED,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC3B,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,YAAY,GAAG,KAAK,KAAK,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;SAC/E;QAED,IAAK,OAAO,CAAC,IAAY,CAAC,IAAI,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;SAC1F;KACJ;SAAM;QACH,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK,KAAK,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACtE;KACJ;AACL,CAAC;AAtBD,sDAsBC"}