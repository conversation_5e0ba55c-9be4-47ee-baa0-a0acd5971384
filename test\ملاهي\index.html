<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>تسجيل الدخول - نظام إدارة الملاهي</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <meta name="color-scheme" content="light dark" />
</head>
<body class="min-h-screen bg-gray-50 flex items-center justify-center p-4 font-sans">
  <main class="w-full max-w-md">
    <div class="bg-white shadow-xl rounded-2xl p-6 border border-gray-100">
      <div class="mb-6 text-center">
        <h1 class="text-2xl font-bold text-gray-800">نظام إدارة الملاهي</h1>
        <p class="text-sm text-gray-500 mt-1">مرحباً! يرجى تسجيل الدخول للمتابعة</p>
      </div>

      <form id="loginForm" class="space-y-4" novalidate>
        <div>
          <label for="username" class="block text-sm font-medium text-gray-700 mb-1">اسم المستخدم</label>
          <input id="username" name="username" type="text" required placeholder="أدخل اسم المستخدم" 
                 class="w-full rounded-xl border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 px-4 py-2.5 transition outline-none bg-white" />
        </div>
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور</label>
          <input id="password" name="password" type="password" required placeholder="••••••••" 
                 class="w-full rounded-xl border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 px-4 py-2.5 transition outline-none bg-white" />
        </div>
        <div class="flex items-center justify-between">
          <label class="inline-flex items-center gap-2 text-sm text-gray-600">
            <input id="remember" type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
            تذكرني
          </label>
          <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 hover:underline">نسيت كلمة المرور؟</a>
        </div>
        <button type="submit" 
                class="w-full bg-indigo-600 text-white rounded-xl py-2.5 font-semibold hover:bg-indigo-700 active:bg-indigo-800 transition shadow-sm">
          تسجيل الدخول
        </button>
      </form>

      <div id="toast" class="hidden mt-4 text-sm rounded-lg p-3 text-white bg-emerald-600"></div>

      <div class="mt-6 text-center text-xs text-gray-500">
        <span>© <span id="year"></span> جميع الحقوق محفوظة</span>
      </div>
    </div>

    <div class="mt-6 text-center text-sm">
      <a href="dashboard.html" class="text-gray-500 hover:text-gray-700 hover:underline">تخطي إلى لوحة التحكم (عرض تجريبي)</a>
    </div>
  </main>

  <script>
    // تعيين السنة الحالية
    document.getElementById('year').textContent = new Date().getFullYear();

    // معالجة تسجيل الدخول (صوري)
    const form = document.getElementById('loginForm');
    const toast = document.getElementById('toast');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();

      if (!username || !password) {
        showToast('الرجاء إدخال اسم المستخدم وكلمة المرور', true);
        return;
      }

      // تخزين حالة الدخول صوريًا
      localStorage.setItem('auth', 'true');
      localStorage.setItem('auth_user', JSON.stringify({ name: username }));
      showToast('تم تسجيل الدخول بنجاح، سيتم تحويلك الآن...');
      setTimeout(() => {
        window.location.href = 'dashboard.html';
      }, 800);
    });

    function showToast(message, isError = false) {
      toast.textContent = message;
      toast.classList.remove('hidden');
      toast.classList.toggle('bg-emerald-600', !isError);
      toast.classList.toggle('bg-rose-600', isError);
      setTimeout(() => toast.classList.add('hidden'), 2500);
    }
  </script>
</body>
</html>

