<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>لوحة التحكم - نظام إدارة الملاهي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap"
      rel="stylesheet"
    />
  </head>
  <body
    class="min-h-screen bg-slate-900 text-slate-100"
    style="font-family: 'Cairo', system-ui, sans-serif"
  >
    <!-- تراك للجوال -->
    <div
      id="overlay"
      class="fixed inset-0 bg-black/40 backdrop-blur-sm opacity-0 pointer-events-none transition md:hidden z-40"
    ></div>

    <!-- الشريط الجانبي يمين -->
    <aside
      id="sidebar"
      class="fixed inset-y-0 right-0 w-72 bg-slate-900/95 border-l border-slate-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out translate-x-full md:translate-x-0"
    >
      <div class="h-full flex flex-col">
        <div
          class="flex items-center justify-between px-4 py-4 border-b border-slate-800"
        >
          <a href="dashboard.html" class="flex items-center gap-2">
            <span
              class="inline-flex items-center justify-center w-9 h-9 rounded-xl bg-indigo-600 text-white"
              >🎡</span
            >
            <span class="text-lg font-bold">الملاهي</span>
          </a>
          <button
            id="closeSidebarBtn"
            class="md:hidden inline-flex items-center justify-center w-9 h-9 rounded-lg hover:bg-slate-800 transition"
            aria-label="إغلاق"
          >
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
              <path
                d="M6 6l12 12M18 6l-12 12"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <nav class="px-2 py-3 flex-1 overflow-y-auto">
          <ul class="space-y-1 text-sm">
            <li>
              <a
                href="dashboard.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg bg-indigo-600 text-white shadow nav-link"
                >لوحة التحكم</a
              >
            </li>
            <li>
              <a
                href="ticket.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >إصدار التذاكر</a
              >
            </li>
            <li>
              <a
                href="scan.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >فحص التذاكر</a
              >
            </li>
            <li>
              <a
                href="games.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >الألعاب</a
              >
            </li>
            <li>
              <a
                href="reports.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >التقارير</a
              >
            </li>
            <li>
              <a
                href="mobile.html"
                class="flex items-center gap-2 px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800/60 transition nav-link"
                >نسخة الموظف</a
              >
            </li>
          </ul>
        </nav>
        <div class="px-3 py-3 border-t border-slate-800 relative">
          <button
            id="profileBtn"
            class="w-full flex items-center justify-between gap-3 px-3 py-2 rounded-lg bg-slate-800/60 hover:bg-slate-800 transition"
          >
            <span class="flex items-center gap-3"
              ><span
                class="inline-flex items-center justify-center w-9 h-9 rounded-full bg-indigo-600 text-white text-sm font-semibold"
                >م</span
              ><span class="text-sm">مرحباً، مدير</span></span
            >
            <svg
              class="w-4 h-4 opacity-80"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06.02L10 11.17l3.71-3.94a.75.75 0 111.08 1.04l-4.24 4.5a.75.75 0 01-1.08 0l-4.24-4.5a.75.75 0 01.02-1.06z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <div
            id="profileMenu"
            class="absolute bottom-16 end-3 w-52 bg-slate-900 border border-slate-800 rounded-xl shadow-lg p-1.5 origin-bottom-right scale-95 opacity-0 pointer-events-none transition z-50"
          >
            <a
              href="#profile"
              class="block px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800 transition"
              >الملف الشخصي</a
            >
            <a
              href="#settings"
              class="block px-3 py-2 rounded-lg text-slate-200 hover:bg-slate-800 transition"
              >الإعدادات</a
            >
            <div class="my-1 border-t border-slate-800"></div>
            <a
              href="index.html"
              class="block px-3 py-2 rounded-lg text-rose-400 hover:bg-rose-500/10 transition"
              >تسجيل الخروج</a
            >
          </div>
        </div>
      </div>
    </aside>

    <!-- شريط علوي للجوال -->
    <header
      class="md:hidden sticky top-0 z-30 bg-slate-900/90 backdrop-blur border-b border-slate-800"
    >
      <div class="px-4 py-3 flex items-center justify-between">
        <button
          id="openSidebarBtn"
          class="inline-flex items-center gap-2 px-3 py-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition"
          aria-label="فتح القائمة"
        >
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
            <path
              d="M4 7h16M4 12h16M4 17h16"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
            /></svg
          >القائمة
        </button>
        <div class="flex items-center gap-2">
          <span
            class="inline-flex items-center justify-center w-8 h-8 rounded-lg bg-indigo-600 text-white"
            >🎡</span
          ><span class="font-semibold">الملاهي</span>
        </div>
        <span class="w-8"></span>
      </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="min-h-screen md:mr-72 p-4 lg:p-6 space-y-6">
      <section class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div
          class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4 shadow-sm"
        >
          <div class="text-slate-400 text-sm">إجمالي التذاكر</div>
          <div class="mt-2 text-2xl font-bold">1,254</div>
          <div class="text-xs text-emerald-400 mt-1">+12% هذا الأسبوع</div>
        </div>
        <div
          class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4 shadow-sm"
        >
          <div class="text-slate-400 text-sm">الزوار اليوم</div>
          <div class="mt-2 text-2xl font-bold">342</div>
          <div class="text-xs text-rose-400 mt-1">-4% عن أمس</div>
        </div>
        <div
          class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4 shadow-sm"
        >
          <div class="text-slate-400 text-sm">الإيرادات</div>
          <div class="mt-2 text-2xl font-bold">‏12,750 ر.س</div>
          <div class="text-xs text-emerald-400 mt-1">+5% هذا الشهر</div>
        </div>
      </section>

      <section class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <div
          class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4 shadow-sm lg:col-span-2"
        >
          <div class="flex items-center justify-between">
            <h2 class="font-bold">الحركة الأسبوعية</h2>
            <div class="text-xs text-slate-400">مخطط توضيحي (عنصر نائب)</div>
          </div>
          <div
            class="mt-4 h-56 bg-gradient-to-tr from-indigo-900/20 to-indigo-500/20 border border-slate-700 rounded-xl flex items-center justify-center text-indigo-300"
          >
            مخطط خطي
          </div>
        </div>
        <div
          class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4 shadow-sm"
        >
          <div class="flex items-center justify-between">
            <h2 class="font-bold">التوزيع حسب اللعبة</h2>
            <div class="text-xs text-slate-400">مخطط دائري (عنصر نائب)</div>
          </div>
          <div
            class="mt-4 h-56 bg-gradient-to-tr from-emerald-900/20 to-emerald-500/20 border border-slate-700 rounded-xl flex items-center justify-center text-emerald-300"
          >
            مخطط دائري
          </div>
        </div>
      </section>

      <section class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div
          class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4 shadow-sm"
        >
          <div class="flex items-center justify-between mb-3">
            <h2 class="font-bold">آخر التذاكر</h2>
            <a
              href="ticket.html"
              class="text-sm text-indigo-400 hover:text-indigo-300"
              >إصدار تذكرة</a
            >
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full text-sm">
              <thead>
                <tr class="text-slate-400">
                  <th class="text-right py-2">رقم</th>
                  <th class="text-right py-2">العميل</th>
                  <th class="text-right py-2">اللعبة</th>
                  <th class="text-right py-2">السعر</th>
                </tr>
              </thead>
              <tbody id="lastTickets"></tbody>
            </table>
          </div>
        </div>
        <div
          class="bg-slate-800/60 border border-slate-700 rounded-2xl p-4 shadow-sm"
        >
          <div class="flex items-center justify-between mb-3">
            <h2 class="font-bold">الألعاب الأكثر شعبية</h2>
            <a
              href="games.html"
              class="text-sm text-indigo-400 hover:text-indigo-300"
              >إدارة الألعاب</a
            >
          </div>
          <ul id="topGames" class="space-y-2 text-sm"></ul>
        </div>
      </section>
    </main>

    <footer class="mt-8 py-6 text-center text-xs text-slate-500">
      © <span id="year"></span> نظام إدارة الملاهي
    </footer>

    <script>
      // حماية بسيطة
      if (localStorage.getItem("auth") !== "true") {
        /* window.location.href='index.html'; */
      }
      document.getElementById("year").textContent = new Date().getFullYear();

      // بيانات صورية
      const tickets = [
        {
          id: "T-1001",
          customer: "أحمد علي",
          game: "القطار السريع",
          price: 50,
        },
        { id: "T-1002", customer: "سارة محمد", game: "بيت الرعب", price: 40 },
        { id: "T-1003", customer: "خالد صالح", game: "عجلة دوارة", price: 30 },
        {
          id: "T-1004",
          customer: "منى حسن",
          game: "سيارات التصادم",
          price: 35,
        },
      ];
      const top = [
        { name: "القطار السريع", count: 120 },
        { name: "بيت الرعب", count: 98 },
        { name: "عجلة دوارة", count: 76 },
      ];
      const lastTicketsEl = document.getElementById("lastTickets");
      lastTicketsEl.innerHTML = tickets
        .map(
          (t) =>
            `<tr class='border-t border-slate-700'><td class='py-2'>${t.id}</td><td>${t.customer}</td><td>${t.game}</td><td>${t.price} ر.س</td></tr>`
        )
        .join("");
      const topGamesEl = document.getElementById("topGames");
      topGamesEl.innerHTML = top
        .map(
          (g) =>
            `<li class='flex items-center justify-between bg-slate-800/60 rounded-lg px-3 py-2'><span>${g.name}</span><span class='text-slate-400'>${g.count} تذكرة</span></li>`
        )
        .join("");

      // فتح/إغلاق الشريط للجوال
      const sidebar = document.getElementById("sidebar");
      const overlay = document.getElementById("overlay");
      const openBtn = document.getElementById("openSidebarBtn");
      const closeBtn = document.getElementById("closeSidebarBtn");
      const openSidebar = () => {
        sidebar.classList.remove("translate-x-full");
        overlay.classList.remove("pointer-events-none");
        overlay.classList.add("opacity-100");
        overlay.classList.remove("opacity-0");
      };
      const closeSidebar = () => {
        sidebar.classList.add("translate-x-full");
        overlay.classList.add("pointer-events-none");
        overlay.classList.remove("opacity-100");
        overlay.classList.add("opacity-0");
      };
      openBtn?.addEventListener("click", openSidebar);
      closeBtn?.addEventListener("click", closeSidebar);
      overlay?.addEventListener("click", closeSidebar);
      const mq = window.matchMedia("(min-width:768px)");
      const handleMQ = () => {
        if (mq.matches) {
          sidebar.classList.remove("translate-x-full");
          overlay.classList.add("pointer-events-none", "opacity-0");
          overlay.classList.remove("opacity-100");
        } else {
          sidebar.classList.add("translate-x-full");
        }
      };
      handleMQ();
      mq.addEventListener?.("change", handleMQ);

      // القائمة المنسدلة للمستخدم
      const profileBtn = document.getElementById("profileBtn");
      const profileMenu = document.getElementById("profileMenu");
      let dropOpen = false;
      const openDrop = () => {
        dropOpen = true;
        profileMenu.classList.remove(
          "opacity-0",
          "scale-95",
          "pointer-events-none"
        );
        profileMenu.classList.add(
          "opacity-100",
          "scale-100",
          "pointer-events-auto"
        );
      };
      const closeDrop = () => {
        dropOpen = false;
        profileMenu.classList.add(
          "opacity-0",
          "scale-95",
          "pointer-events-none"
        );
        profileMenu.classList.remove(
          "opacity-100",
          "scale-100",
          "pointer-events-auto"
        );
      };
      closeDrop();
      profileBtn?.addEventListener("click", (e) => {
        e.stopPropagation();
        dropOpen ? closeDrop() : openDrop();
      });
      document.addEventListener("click", (e) => {
        if (
          dropOpen &&
          !profileMenu.contains(e.target) &&
          !profileBtn.contains(e.target)
        )
          closeDrop();
      });
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape") {
          closeDrop();
          if (!mq.matches) closeSidebar();
        }
      });
    </script>
  </body>
</html>
